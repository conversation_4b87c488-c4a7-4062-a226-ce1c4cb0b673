{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?17a0", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?4c52", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?ee2f", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?6227", "uni-app:///pagesGoEasy/chat_page/components/item/quoteType/m-text.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?5268", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/chat_page/components/item/quoteType/m-text.vue?038d"], "names": ["props", "isMy", "type", "default", "value", "data", "created", "computed", "renderTextMessage", "text", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,4qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACY7wB;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;EACAC;IACA;IACA;IACAC;MACA;QAAAC;MACAA;MACA;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAAo7C,CAAgB,muCAAG,EAAC,C;;;;;;;;;;;ACAx8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/components/item/quoteType/m-text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-text.vue?vue&type=template&id=4f21a75e&scoped=true&\"\nvar renderjs\nimport script from \"./m-text.vue?vue&type=script&lang=js&\"\nexport * from \"./m-text.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-text.vue?vue&type=style&index=0&id=4f21a75e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f21a75e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/item/quoteType/m-text.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=template&id=4f21a75e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"quote-box\">\n\t\t<view class=\"quote-name\">\n\t\t\t<text>\n\t\t\t\t{{ value.senderData.name }}：\n\t\t\t\t<text v-html=\"renderTextMessage\"></text>\n\t\t\t</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { EmojiDecoder, emojiMap } from '../../../../lib/EmojiDecoder.js';\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/';\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap);\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcreated() {},\n\tcomputed: {\n\t\t//渲染文本消息，如果包含表情，替换为图片\n\t\t//todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\n\t\trenderTextMessage() {\n\t\t\tlet { text = '' } = this.value.payload;\n\t\t\ttext = text.replace(/\\n/g, '');\n\t\t\tif (!text) return '<span>' + '[未知内容]' + '</span>';\n\t\t\treturn '<span>' + decoder.decode(text) + '</span>';\n\t\t}\n\t},\n\tmethods: {}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.quote-box {\n\tbox-sizing: border-box;\n\tpadding: 12rpx 16rpx;\n\tborder-radius: 10rpx;\n\tmargin-top: 6rpx;\n\tbackground-color: #e1e1e1;\n\tcolor: #6b6b6b;\n}\n.quote-name {\n}\n.quote-content {\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=style&index=0&id=4f21a75e&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=style&index=0&id=4f21a75e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137555431\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}