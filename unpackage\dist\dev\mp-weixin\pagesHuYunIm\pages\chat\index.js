(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesHuYunIm/pages/chat/index"],{

/***/ 74:
/*!********************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/huyun-im/main.js?{"page":"pagesHuYunIm%2Fpages%2Fchat%2Findex"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pagesHuYunIm/pages/chat/index.vue */ 75));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 75:
/*!***********************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=00dcdc92&scoped=true& */ 76);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 78);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& */ 80);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 43);

var renderjs





/* normalize component */

var component = Object(_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "00dcdc92",
  null,
  false,
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesHuYunIm/pages/chat/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 76:
/*!******************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=template&id=00dcdc92&scoped=true& ***!
  \******************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=00dcdc92&scoped=true& */ 77);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 77:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=template&id=00dcdc92&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    mGroupSelection: function () {
      return __webpack_require__.e(/*! import() | components/m-group-selection/m-group-selection */ "components/m-group-selection/m-group-selection").then(__webpack_require__.bind(null, /*! @/components/m-group-selection/m-group-selection.vue */ 234))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.list, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var m0 =
      !item.recalled && item.msgType !== "withdraw"
        ? _vm.renderMessageDate(item, index)
        : null
    var m1 =
      !item.recalled && item.msgType !== "withdraw" && !item.recalled
        ? _vm.isSelf(item.userId)
        : null
    var m2 =
      !item.recalled &&
      item.msgType !== "withdraw" &&
      !!item.recalled &&
      item.recalled &&
      item.msgType === "withdraw"
        ? _vm.getByIdUser(item.userId)
        : null
    var m3 =
      !item.recalled &&
      item.msgType !== "withdraw" &&
      !!item.recalled &&
      !(item.recalled && item.msgType === "withdraw")
        ? _vm.isSelf(item.senderId)
        : null
    var m4 =
      !item.recalled &&
      item.msgType !== "withdraw" &&
      !!item.recalled &&
      !(item.recalled && item.msgType === "withdraw")
        ? item.msgType === "text" && _vm.isSelf(item.senderId)
        : null
    return {
      $orig: $orig,
      m0: m0,
      m1: m1,
      m2: m2,
      m3: m3,
      m4: m4,
    }
  })
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 78:
/*!************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 79);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 79:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 59));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 61));
var _vuex = __webpack_require__(/*! vuex */ 38);
var _public = __webpack_require__(/*! ../../api/public */ 65);
var _mqttClient = _interopRequireDefault(__webpack_require__(/*! ../../utils/mqttClient.js */ 62));
var _mqttMin = _interopRequireDefault(__webpack_require__(/*! ../../lib/mqtt.min.js */ 64));
var _index = _interopRequireDefault(__webpack_require__(/*! ../../store/index.js */ 68));
var _utils = __webpack_require__(/*! ../../utils */ 70);
var _index2 = __webpack_require__(/*! @/utils/index.js */ 30);
var _mqttConfig = __webpack_require__(/*! ../../utils/mqttConfig.js */ 63);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var navigation = function navigation() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/navigation/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/navigation/index")]).then((function () {
    return resolve(__webpack_require__(/*! ./components/navigation/index.vue */ 243));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var bottomOperation = function bottomOperation() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/bottom-operation/index */ "pagesHuYunIm/pages/chat/components/bottom-operation/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/bottom-operation/index.vue */ 250));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var item = function item() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/index */ "pagesHuYunIm/pages/chat/components/item/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/item/index */ 257));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var videoPlayerRef = function videoPlayerRef() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/video-player/index */ "pagesHuYunIm/pages/chat/components/video-player/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/video-player/index */ 264));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var openRedPacket = function openRedPacket() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/open-red-packet/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/open-red-packet/index")]).then((function () {
    return resolve(__webpack_require__(/*! ./components/open-red-packet/index */ 271));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var operate = function operate() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/operate/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/operate/index")]).then((function () {
    return resolve(__webpack_require__(/*! ./components/operate/index */ 278));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var lastMessageTimeStamp = null;
var innerAudioContext = null;
var audioItem = {};
var group = {};

// 安全初始化音频上下文
try {
  innerAudioContext = uni.createInnerAudioContext();
} catch (error) {
  console.warn('初始化音频上下文失败:', error);
  innerAudioContext = null;
}
var groupId = null;

// 浏览照片数组
var imageList = [];

// 是否是手动触发的列表滑动
var isBottomOperationScrollToBottom = false;
var IMAGE_MAX_WIDTH = 200;
var IMAGE_MAX_HEIGHT = 150;
var scroll_top = 0;
var reserveHeightRef = 0;
var _default = {
  components: {
    // groupSelection,
    navigation: navigation,
    bottomOperation: bottomOperation,
    item: item,
    videoPlayerRef: videoPlayerRef,
    openRedPacket: openRedPacket,
    operate: operate
  },
  name: 'groupChat',
  data: function data() {
    return {
      isHistoryGet: false,
      reserveHeight: 0,
      keyboardheightchangeValue: 0,
      myid: null,
      scroll_top: 0,
      userList: [],
      //群成员列表
      groupCount: '',
      pagueObj: {
        name: '饭搭子5人组'
      },
      to: {},
      // 历史数据
      history: {
        messages: [],
        allLoaded: false
      },
      videoPlayer: {
        show: false,
        url: '',
        context: null
      },
      // 添加缺失的数据属性
      page: 1,
      pageSize: 50,
      groupId: '',
      loading: false,
      loadend: false,
      list: [],
      userMap: {},
      mqttClient: null,
      mqttPingInterval: null,
      userArr: [],
      //群成员
      groupInfo: {},
      //群信息
      userInfo: {},
      //用户信息
      scrollHeight: 0
    };
  },
  onLoad: function onLoad(e) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var groupInfo;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              console.log('🚀 ~ onLoad ~ e:', e);
              groupInfo = JSON.parse(decodeURIComponent(e.groupInfo));
              _this.groupInfo = groupInfo;
              _this.userInfo = JSON.parse(decodeURIComponent(e.userInfo));
              _this.userArr = groupInfo.userArr;
              // 设置MQTT库
              _mqttClient.default.setMqttLib(_mqttMin.default);
              _this.groupId = groupInfo.id;
              //
              scroll_top = 0;
              _this.scroll_top = scroll_top;
              imageList = [];
              lastMessageTimeStamp = e.lastMessageTimeStamp || null;
              _this.isHistoryGet = e.lastMessageTimeStamp;
              groupId = groupInfo.id;
              //
              _this.myid = _this.userInfo.userId;
              _this.initMqtt();
              _this.loadHistoryMessage();
              _this.initScrollHeight();
              _this.setHeight();
            case 18:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  onPageScroll: function onPageScroll() {
    this.$refs.bottomOperationRef.closeAll();
  },
  onReady: function onReady() {
    this.videoPlayer.context = uni.createVideoContext('videoPlayer', this);
  },
  onUnload: function onUnload() {
    // 页面卸载时清理资源
    _mqttClient.default.disconnect();
    // 清理音频资源
    if (innerAudioContext && typeof innerAudioContext.destroy === 'function') {
      try {
        innerAudioContext.destroy();
      } catch (error) {
        console.warn('清理音频资源失败:', error);
      } finally {
        innerAudioContext = null;
      }
    }
  },
  computed: (0, _vuex.mapState)({
    page_font_size: function page_font_size(state) {
      return state.page_font_size;
    },
    //显示时间
    renderMessageDate: function renderMessageDate() {
      var _this2 = this;
      return function (message, index) {
        // 检查 createTime 是否存在
        if (!message.createTime) {
          return '';
        }
        //正则替换 -
        var createTimeStamp = new Date(message.createTime.replace(/-/g, '/')).getTime();
        // 第一条消息总是显示时间
        if (index === 0) {
          return message.createTime;
        }
        // 获取前一条消息
        var prevMessage = _this2.list[index - 1];
        if (prevMessage && prevMessage.createTime) {
          var prevCreateTimeStamp = new Date(prevMessage.createTime.replace(/-/g, '/')).getTime();
          // 如果当前消息比前一条消息晚3分钟以上，则显示时间
          if (createTimeStamp - prevCreateTimeStamp > 3 * 60 * 1000) {
            return message.createTime;
          }
        }
        return '';
      };
    },
    // 是否本人isMy
    isSelf: function isSelf() {
      var _this3 = this;
      return function (senderId) {
        var _this3$userInfo$userI = _this3.userInfo.userId,
          userId = _this3$userInfo$userI === void 0 ? '' : _this3$userInfo$userI;
        return senderId === "".concat(userId);
      };
    },
    envelope_top_opened: function envelope_top_opened() {
      var _this4 = this;
      return function (id) {
        return _this4.envelopeXollectionList.includes(id);
      };
    }
  }),
  methods: {
    /**
     * 加载消息数据
     * @param {string} idEnd - 结束ID，用于分页加载
     * @returns {Promise<void>}
     */
    loadHistoryMessage: function loadHistoryMessage() {
      var _arguments = arguments,
        _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var idEnd, requestData, _yield$msglist, _yield$msglist2, ress, err, res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                idEnd = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : '';
                if (!(_this5.loading || _this5.loadend)) {
                  _context2.next = 3;
                  break;
                }
                return _context2.abrupt("return");
              case 3:
                _context2.prev = 3;
                _this5.loading = true;

                // 构建请求参数
                requestData = _objectSpread({
                  page: _this5.page,
                  pageSize: _this5.pageSize,
                  groupId: _this5.groupId
                }, idEnd && {
                  id_end: idEnd
                });
                _context2.next = 8;
                return (0, _public.msglist)(requestData);
              case 8:
                _yield$msglist = _context2.sent;
                _yield$msglist2 = (0, _slicedToArray2.default)(_yield$msglist, 2);
                ress = _yield$msglist2[0];
                err = _yield$msglist2[1];
                res = ress.slice(47);
                if (!err) {
                  _context2.next = 17;
                  break;
                }
                console.error('加载消息数据失败:', err);
                uni.showToast({
                  title: '加载失败，请重试',
                  icon: 'none'
                });
                return _context2.abrupt("return");
              case 17:
                if (res && Array.isArray(res)) {
                  // 处理消息内容
                  _this5.processMessages(res);
                  // 合并数据到列表
                  if (idEnd) {
                    // 分页加载：将历史消息添加到列表开头
                    _this5.list = (0, _utils.SplitArray)(res, []).concat(_this5.list);
                  } else {
                    // 初始加载：直接设置列表
                    _this5.list = (0, _utils.SplitArray)(res, _this5.list);
                  }
                  // 检查是否已加载完所有数据
                  _this5.loadend = res.length < _this5.pageSize;
                  console.log('消息数据加载成功:', res);
                  // 如果不是分页加载，滚动到底部
                  if (!idEnd) {
                    _this5.$nextTick(function () {
                      _this5.initContentHeight();
                    });
                  }
                }
                _context2.next = 24;
                break;
              case 20:
                _context2.prev = 20;
                _context2.t0 = _context2["catch"](3);
                console.error('loadData 异常:', _context2.t0);
                uni.showToast({
                  title: '网络异常，请重试',
                  icon: 'none'
                });
              case 24:
                _context2.prev = 24;
                _this5.loading = false;
                return _context2.finish(24);
              case 27:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[3, 20, 24, 27]]);
      }))();
    },
    /**
     * 初始化MQTT连接
     */
    initMqtt: function initMqtt() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var userInfo, mqttUserInfo, callbacks;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                try {
                  // 从store获取用户信息，如果没有则使用默认值
                  userInfo = (0, _mqttConfig.createUserInfo)('1921822887908581377',
                  // userId
                  '范发发',
                  // nickname
                  'hbs119',
                  // channelCode
                  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTE0NTg4MX0.oTkYdEi7Ewms2SxOfRP3uKaMUz9YzOTDtQDIpZeG0_8',
                  // token
                  'https://dummyimage.com/100x100/cccccc/ffffff?text=头像',
                  // avatar
                  'DEV' // 环境
                  ); // 创建用户信息对象
                  mqttUserInfo = (0, _mqttConfig.createUserInfo)(_this6.userInfo.userId, _this6.userInfo.nickname || '用户', _this6.userInfo.channelCode, _this6.userInfo.token, _this6.userInfo.avatar || _this6.defaultAvatar, 'DEV');
                  callbacks = {
                    onConnect: function onConnect() {
                      console.log('MQTT连接成功');
                      _this6.isConnected = true;
                    },
                    onMessage: function onMessage(topic, mqttMsg) {
                      _this6.handleMqttMessage(mqttMsg);
                    },
                    onReconnect: function onReconnect() {
                      console.log('MQTT重连中...');
                      _this6.isConnected = false;
                    },
                    onError: function onError(error) {
                      console.error('MQTT连接错误:', error);
                      _this6.isConnected = false;
                    },
                    onEnd: function onEnd() {
                      console.log('MQTT连接已断开');
                      _this6.isConnected = false;
                    }
                  }; // 连接MQTT
                  _mqttClient.default.connect(mqttUserInfo, callbacks);
                } catch (error) {
                  console.error('初始化MQTT失败:', error);
                  _this6.isConnected = false;
                }
              case 1:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    /**
     * 处理消息内容
     * @param {Array} messages - 消息列表
     */
    processMessages: function processMessages(messages) {
      var _this7 = this;
      messages.forEach(function (item) {
        item.recalled = false;
        var findUser = _this7.userArr.find(function (user) {
          return user.userId === item.userId;
        });
        // 处理发送者数据
        if (findUser) {
          item.senderData = {
            name: findUser.nickname,
            avatar: findUser.avatar,
            member_id: findUser.userId,
            group_id: item.groupId
          };
        }
        item.content = JSON.parse(item.content);
        // 处理语音消息内容
        if (item.msgType === 'voice') {
          console.log('🚀 ~ processMessages ~ item:', item);
          item.payload = {
            url: item.content.url,
            duration: item.content.duration
          };
        } else if (item.msgType === 'image') {
          item.payload = {
            url: item.content.url
          };
        } else if (item.msgType === 'withdraw') {
          item.recalled = true;
          item.payload = {
            text: item.status
          };
        } else {
          item.payload = {
            text: item.content.msg
          };
        }
      });
    },
    /**
     * 获取音频文件时长
     * @param {string} audioUrl - 音频文件URL
     * @returns {Promise<number>} 音频时长（秒）
     */
    getAudioDuration: function getAudioDuration(audioUrl) {
      return new Promise(function (resolve, reject) {
        console.log('开始获取音频时长，URL:', audioUrl);

        // 在uni-app中使用createInnerAudioContext获取音频时长
        var audioContext = uni.createInnerAudioContext();
        var isResolved = false;

        // 清理函数
        var cleanup = function cleanup() {
          if (audioContext && typeof audioContext.destroy === 'function') {
            try {
              audioContext.destroy();
            } catch (error) {
              console.warn('清理音频上下文失败:', error);
            }
          }
        };

        // 设置音频源
        audioContext.src = audioUrl;
        console.log('已设置音频源:', audioUrl);

        // 监听音频加载完成事件
        audioContext.onCanplay(function () {
          if (isResolved) return;

          // 获取音频时长
          var duration = audioContext.duration;
          console.log('音频时长:', duration, '秒', 'URL:', audioUrl);
          cleanup();
          if (duration && duration > 0) {
            isResolved = true;
            resolve(Math.ceil(duration)); // 向上取整
          } else {
            isResolved = true;
            reject(new Error('无法获取音频时长'));
          }
        });

        // 监听音频加载事件（备用方案）
        audioContext.onLoadedmetadata && audioContext.onLoadedmetadata(function () {
          if (isResolved) return;
          var duration = audioContext.duration;
          console.log('通过loadedmetadata获取音频时长:', duration, '秒');
          if (duration && duration > 0) {
            cleanup();
            isResolved = true;
            resolve(Math.ceil(duration));
          }
        });

        // 监听错误事件
        audioContext.onError(function (error) {
          if (isResolved) return;
          console.error('音频加载失败:', error, 'URL:', audioUrl);
          cleanup();
          isResolved = true;
          reject(error);
        });

        // 设置超时处理
        setTimeout(function () {
          if (!isResolved) {
            cleanup();
            isResolved = true;
            reject(new Error('获取音频时长超时'));
          }
        }, 8000); // 8秒超时
      });
    },
    /**
     * 处理MQTT消息
     * @param {string} _topic - 消息主题（暂未使用）
     * @param {Buffer} message - 消息内容
     */
    handleMqttMessage: function handleMqttMessage(_topic, message) {
      try {
        var messageStr = message.toString();
        console.log('收到MQTT消息:', messageStr);
        var mqttMsg = JSON.parse(messageStr);
        if (mqttMsg.command === 'chatMsg') {
          this.processChatMessage(mqttMsg.data);
        }
      } catch (error) {
        console.error('处理MQTT消息失败:', error);
      }
    },
    /**
     * 处理聊天消息
     * @param {Object} chatMsg - 聊天消息数据
     */
    processChatMessage: function processChatMessage(chatMsg) {
      // 如果消息格式不完整，转换为标准格式
      if (!chatMsg.id || !chatMsg.senderData) {
        var standardMessage = this.convertToStandardFormat(chatMsg);
        chatMsg = standardMessage;
      }
      if (this.groupId === chatMsg.groupId) {
        // 当前群组消息，直接添加到列表（不发送MQTT，因为是接收到的消息）
        this.addMessageToList(chatMsg);
        // 标记消息为已读
        this.markMessageAsRead(chatMsg.groupId);
      } else {
        // 其他群组消息，显示通知
      }
    },
    /**
     * 直接添加消息到列表（用于接收消息，不发送MQTT）
     * @param {Object} message - 消息对象
     */
    addMessageToList: function addMessageToList(message) {
      this.initMessageItem(message);

      // 监听到公告
      if (message.type === 'group_notice') {
        console.log('监听到公告');
        this.$refs.navigationRef.getData();
      }
      // 监听到修改群名
      if (message.type === 'update_group_name') {
        console.log('监听到修改群名');
        this.pagueObj.name = message.payload.name;
      }
      // 将新消息添加到列表末尾（最新消息在底部）
      this.list.push(message);
      this.initContentHeight(true);
      // 是否触发文字动效果
      if (message.type === 'text' || message.type === 'text_quote') {
        this.onSetText(message.payload.text);
      }
      // 是否触发红包雨
      if (message.type === 'red_envelope') {
        this.onSetRedEnvelope();
      }

      // 缓存照片地址，
      if (message.type === 'image' || message.type === 'image_transmit') {
        imageList.push(message.payload.url);
      }
    },
    /**
     * 将旧格式消息转换为标准格式
     * @param {Object} oldMsg - 旧格式消息
     * @returns {Object} 标准格式消息
     */
    convertToStandardFormat: function convertToStandardFormat(oldMsg) {
      var _oldMsg$payload, _this$userMap$oldMsg$, _this$userMap$oldMsg$2;
      var now = new Date();
      var createTime = oldMsg.createTime || now.toISOString().slice(0, 19).replace('T', ' ');
      var messageId = oldMsg.id || oldMsg.messageId || "".concat(oldMsg.groupId, "_").concat(Date.now().toString().slice(-6));
      return {
        content: oldMsg.content || ((_oldMsg$payload = oldMsg.payload) === null || _oldMsg$payload === void 0 ? void 0 : _oldMsg$payload.text) || '',
        createBy: null,
        createTime: createTime,
        groupId: oldMsg.groupId,
        id: messageId,
        msgType: oldMsg.msgType || oldMsg.type || 'text',
        payload: oldMsg.payload || {
          text: oldMsg.content
        },
        senderData: oldMsg.senderData || {
          avatar: ((_this$userMap$oldMsg$ = this.userMap[oldMsg.userId]) === null || _this$userMap$oldMsg$ === void 0 ? void 0 : _this$userMap$oldMsg$.avatar) || '',
          group_id: oldMsg.groupId,
          member_id: oldMsg.userId,
          name: ((_this$userMap$oldMsg$2 = this.userMap[oldMsg.userId]) === null || _this$userMap$oldMsg$2 === void 0 ? void 0 : _this$userMap$oldMsg$2.nickname) || oldMsg.nickname || ''
        },
        status: '正常',
        sysOrgCode: null,
        updateBy: null,
        updateTime: null,
        userId: oldMsg.userId
      };
    },
    /**
     * 标记消息为已读
     * @param {string} groupId - 群组ID
     */
    markMessageAsRead: function markMessageAsRead(groupId) {
      if (this.mqttClient && this.mqttClient.connected) {
        var userInfo = _index.default.state.app.userInfo;
        var readMessage = JSON.stringify({
          groupId: groupId
        });
        this.mqttClient.publish("/chat/server/".concat(userInfo.userId, "/read"), readMessage);
      }
    },
    /**
     * 清除心跳定时器
     */
    clearPingInterval: function clearPingInterval() {
      if (this.mqttPingInterval) {
        clearInterval(this.mqttPingInterval);
        this.mqttPingInterval = null;
      }
    },
    /**
     * 断开MQTT连接
     */
    disconnectMqtt: function disconnectMqtt() {
      this.clearPingInterval();
      if (this.mqttClient) {
        this.mqttClient.end();
        this.mqttClient = null;
      }
    },
    setHeight: function setHeight(e) {
      // 只计算导航栏的高度
      var customBar = this.$store.state.StatusBar.customBar;
      this.reserveHeight = customBar;
      reserveHeightRef = customBar;
    },
    // getHeight(e) {
    //   this.$nextTick(() => {
    //     let view = uni.createSelectorQuery().select('.messageList_')
    //     view
    //       .boundingClientRect((select) => {
    //         if (!select) return
    //         if (!select?.height) {
    //           this.$nextTick(() => {
    //             let view2 = uni.createSelectorQuery().select('.messageList_')
    //             view2
    //               .boundingClientRect((select) => {
    //                 this.setHeight(select.height)
    //                 if (e) {
    //                   setTimeout(() => {
    //                     this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
    //                   })
    //                 }
    //               })
    //               .exec()
    //           })
    //         } else {
    //           this.setHeight(select.height)
    //           if (e) {
    //             setTimeout(() => {
    //               this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
    //             })
    //           }
    //         }
    //       })
    //       .exec()
    //   })
    // },
    //图片加载完成
    imgLoad: function imgLoad() {
      if (this.list.length > 20) return;
      this.initContentHeight(true);
    },
    keyboardheightchange: function keyboardheightchange(e) {
      var e2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      this.keyboardheightchangeValue = e;
      if (reserveHeightRef) {
        this.reserveHeight = reserveHeightRef - e;
      }
      if (e === 0) {
        if (e2) return;
        this.initContentHeight();
      }
    },
    // 点击整个页面
    onPage: function onPage() {
      this.$refs.bottomOperationRef.close();
      this.$refs.operateRef.close();
    },
    touchmove: function touchmove() {
      // this.$refs.bottomOperationRef.closeAll();
    },
    onBottom: function onBottom() {
      this.$refs.operateRef.close();
    },
    // 输入框获取焦点
    focus: function focus() {
      if (this.isHistoryGet) {
        this.isHistoryGet = false;
        lastMessageTimeStamp = null;
        this.list = [];
        this.loadHistoryMessage();
      }
    },
    onMessageReceived: function onMessageReceived(message) {
      if (message.groupId === group.id) {
        // push进列表
        this.pushList(message);
        //聊天时，收到消息标记为已读
        this.markGroupMessageAsRead();
      }
    },
    // 转发成功后
    sendMessage: function sendMessage(message) {
      // push进列表
      if (message.groupId === groupId) {
        this.pushList(message);
        // 同步消息到首页
        uni.$emit('onMessageReceived', message);
      }
    },
    // 将信息设置为已读
    markGroupMessageAsRead: function markGroupMessageAsRead() {
      //
    },
    // 组装item
    initMessageItem: function initMessageItem(message, index) {
      message['isHide'] = 0;
      // 初始化语音
      if (message.type === 'audio') {
        message['pause'] = 4;
      }
    },
    // 发送信息后，将信息push到列表
    pushList: function pushList(message) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _this8.initMessageItem(message);
                // 发送MQTT消息到服务器
                _context4.next = 3;
                return _this8.publishMessageToMqtt(message);
              case 3:
                // 监听到公告
                if (message.type === 'group_notice') {
                  console.log('监听到公告');
                  _this8.$refs.navigationRef.getData();
                }
                // 监听到修改群名
                if (message.type === 'update_group_name') {
                  console.log('监听到修改群名');
                  _this8.pagueObj.name = message.payload.name;
                }

                // 将新消息添加到列表末尾（最新消息在底部）
                _this8.list.push(message);
                _this8.initContentHeight(true);

                // 是否触发文字动效果
                if (message.type === 'text' || message.type === 'text_quote') {
                  _this8.onSetText(message.payload.text);
                }
                // 是否触发红包雨
                if (message.type === 'red_envelope') {
                  _this8.onSetRedEnvelope();
                }

                // 缓存照片地址，
                if (message.type === 'image' || message.type === 'image_transmit') {
                  imageList.push(message.payload.url);
                }
              case 10:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    /**
     * 发布消息到MQTT队列
     * @param {Object} message - 消息对象
     */
    publishMessageToMqtt: function publishMessageToMqtt(message) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var mqttMessage, topic, success;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                console.log('🚀 ~ publishMessageToMqtt ~ message:', message);
                _context5.prev = 1;
                if (_mqttClient.default.getConnectStatus()) {
                  _context5.next = 5;
                  break;
                }
                console.warn('MQTT未连接，无法发送消息');
                return _context5.abrupt("return", false);
              case 5:
                // 构建MQTT消息格式
                mqttMessage = {
                  groupId: message.groupId,
                  msgType: message.msgType,
                  content: message.content,
                  localMsgId: message.id,
                  userId: message.userId
                }; //  const mqttMessage = {
                //   data: {
                //     groupId: message.groupId,
                //     msgType: message.msgType,
                //     content: message.content,
                //     localMsgId: message.id,
                //     userId: message.userId
                //   },
                //   command: 'chatMsg' //chatMsg 为聊天内容, withdraw为撤回消息的推送
                // }
                // 发布到服务器主题
                topic = "/chat/server/".concat(_this9.userInfo.userId, "/msg");
                console.log('🚀 ~ publishMessageToMqtt ~ topic:', topic);
                success = _mqttClient.default.publish(topic, mqttMessage);
                console.log('🚀 ~ publishMessageToMqtt ~ success:', success);
                if (success) {
                  console.log('MQTT消息发送成功:', mqttMessage);
                } else {
                  console.error('MQTT消息发送失败');
                }
                return _context5.abrupt("return", success);
              case 14:
                _context5.prev = 14;
                _context5.t0 = _context5["catch"](1);
                console.error('发送MQTT消息异常:', _context5.t0);
                return _context5.abrupt("return", false);
              case 18:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[1, 14]]);
      }))();
    },
    /**
     * 撤回消息
     * @param text
     */
    recallMessage: function recallMessage(item) {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var mqttMessage, topic, success;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                console.log('🚀 ~ recallMessage ~ item:', item);
                _context6.prev = 1;
                if (_mqttClient.default.getConnectStatus()) {
                  _context6.next = 5;
                  break;
                }
                console.warn('MQTT未连接，无法发送消息');
                return _context6.abrupt("return", false);
              case 5:
                // 构建MQTT消息格式
                mqttMessage = {
                  id: item.id,
                  //这是消息的ID
                  groupId: item.groupId //群ID
                }; // 发布到服务器主题
                topic = "/chat/server/".concat(_this10.userInfo.userId, "/withdraw");
                success = _mqttClient.default.publish(topic, mqttMessage);
                if (success) {
                  console.log('MQTT消息发送成功:', mqttMessage);
                  _this10.list.forEach(function (i) {
                    console.log('🚀 ~ recallMessage ~ i:', i.id === item.id);
                    if (i.id === item.id) {
                      i.recalled = true;
                    }
                  });
                } else {
                  console.error('MQTT消息发送失败');
                }
                return _context6.abrupt("return", success);
              case 12:
                _context6.prev = 12;
                _context6.t0 = _context6["catch"](1);
                console.error('发送MQTT消息异常:', _context6.t0);
                return _context6.abrupt("return", false);
              case 16:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[1, 12]]);
      }))();
    },
    // 文本触发效果相关========
    onSetText: function onSetText(text) {
      var _this11 = this;
      // 触发礼花
      (0, _index2.throttle)(function () {
        if (text.includes('[彩带]')) {
          _this11.$refs.mScreenAnimationLihua.show();
          uni.vibrateLong();
        }
      }, 4000);
    },
    // 触发红包雨
    onSetRedEnvelope: function onSetRedEnvelope() {
      (0, _index2.throttle)(function () {
        uni.vibrateLong();
      }, 4000);
    },
    bottomOperationScrollToBottom: function bottomOperationScrollToBottom() {
      var _this12 = this;
      isBottomOperationScrollToBottom = true;
      setTimeout(function () {
        isBottomOperationScrollToBottom = false;
        _this12.initContentHeight();
      }, 800);
    },
    // 点击某条信息
    onItem: function onItem(item) {
      var _this13 = this;
      console.log(item);
      switch (item.msgType) {
        case 'video':
          this.playVideo(item);
          break;
        case 'voice':
          this.playAudio(item);
          break;
        case 'audio_quote':
          this.playAudio(item);
          break;
        case 'image':
        case 'image_transmit':
          var index = imageList.indexOf(item.payload.url);
          if (index === -1) return (0, _index2.openimg)(imageList.length - 1, imageList);
          (0, _index2.openimg)(index, imageList);
          break;
        case 'red_envelope':
          // 点击红包
          var fun = function fun(code) {
            _this13.renewItem(code, item);
          };
          uni.$off('open_red_packet');
          uni.$on('open_red_packet', fun);
          item['id'] = group.id;
          break;
        case 'map':
          (0, _index2.getLocation)({
            name: item.payload.title,
            address: item.payload.address,
            latitude: item.payload.latitude,
            longitude: item.payload.longitude
          });
          break;
        case 'article':
          (0, _index2.to)("/pagesOne/HTML/index?id=".concat(item.payload.id));
          break;
        case 'share_SBCF':
          (0, _index2.to)('/pagesSBCF/commodity_list/index', {
            id: item.payload.seller_id
          });
          break;
        case 'share_mall':
          (0, _index2.to)("/pagesShopping/details/index", {
            goods_id: item.payload.goods_id
          });
          break;
        case 'functional_module':
          (0, _index2.to)(item.payload.url);
          break;
        default:
          break;
      }
    },
    // 点击红包后更新那一条
    renewItem: function renewItem(code, item) {
      if (code === '0') {
        // 领取
        item.had_draw = 1;
      } else {
        item.isClick = 1;
      }
      // 不这样写某些情况下更新不了视图，
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].messageId == item.messageId) {
          this.$set(this.list, i, _objectSpread({}, item));
          break;
        }
      }
    },
    // 长按相关=======================
    // 长按某一条
    onLongpress: function onLongpress(item, e) {
      this.$refs.operateRef.open(item, e);
    },
    // 引用
    quote: function quote(item) {
      this.$refs.bottomOperationRef.quote(item);
    },
    // 谢谢红包
    thank: function thank(item) {
      this.$refs.bottomOperationRef.thank(item);
    },
    // 转发
    transmit: function transmit(item) {
      this.$refs.groupSelectionRef.open(item);
    },
    // 重新编辑
    recalledEdit: function recalledEdit(item) {
      this.$refs.bottomOperationRef.recalledEdit(item);
    },
    // @某人
    mention: function mention(item) {
      this.$refs.bottomOperationRef.mention(item);
    },
    // 视频相关========================
    // 点击了视频并播放
    playVideo: function playVideo(item) {
      var _this14 = this;
      this.videoPlayer.url = item.payload.video.url;
      this.videoPlayer.show = true;
      this.$nextTick(function () {
        _this14.videoPlayer.context.requestFullScreen({
          direction: 0
        });
        _this14.videoPlayer.context.play();
        _this14.videoPlayer.context.showStatusBar();
      });
    },
    // 退出全屏
    onVideoFullScreenChange: function onVideoFullScreenChange(e) {
      //当退出全屏播放时，隐藏播放器
      if (this.videoPlayer.show && !e.detail.fullScreen) {
        this.videoPlayer.show = false;
        this.videoPlayer.context.stop();
      }
    },
    // =============================================
    // 播放语音相关===========
    playAudio: function playAudio(item) {
      (0, _index2.throttle)(function () {
        var _audioItem;
        // pause:1暂停;2播放完,3播放中,4初始状态
        if (item.id === ((_audioItem = audioItem) === null || _audioItem === void 0 ? void 0 : _audioItem.id)) {
          if (audioItem['pause'] == 3) {
            //正在播放
            // 暂停
            innerAudioContext.pause();
            innerAudioContext.offEnded();
            item['pause'] = 1;
            audioItem['pause'] = 1;
          } else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {
            //暂停或者播放中
            // 播放
            innerAudioContext.play();
          }
          return;
        }
        audioItem['pause'] = '4';
        audioItem = item;
        if (innerAudioContext) {
          try {
            if (typeof innerAudioContext.pause === 'function') {
              innerAudioContext.pause();
            }
            if (typeof innerAudioContext.destroy === 'function') {
              innerAudioContext.destroy();
            }
          } catch (e) {
            console.warn('清理音频上下文失败:', e);
          } finally {
            innerAudioContext = null;
          }
        }

        // 安全创建新的音频上下文
        try {
          innerAudioContext = uni.createInnerAudioContext();
        } catch (error) {
          console.error('创建音频上下文失败:', error);
          return;
        }
        if (!innerAudioContext) {
          console.error('音频上下文创建失败');
          return;
        }
        console.log('准备播放音频，URL:', item.payload.url);
        console.log('音频item信息:', item);
        innerAudioContext.src = item.payload.url;
        innerAudioContext.play();
        innerAudioContext.offEnded();
        innerAudioContext.offPlay();
        innerAudioContext.onPlay(function () {
          console.log('开始播放音频:', item.payload.url);
          item['pause'] = 3;
          audioItem['pause'] = 3;
        });
        innerAudioContext.onEnded(function () {
          console.log('音频播放结束:', item.payload.url);
          item['pause'] = 2;
          audioItem['pause'] = 2;
        });
        innerAudioContext.onError(function (res) {
          console.error('音频播放异常:', res, 'URL:', item.payload.url);
        });
      }, 500);
    },
    // ====================
    // 滚动中
    scroll: function scroll(e) {
      scroll_top = e.detail.scrollTop;
      this.$refs.operateRef.close();
      if (isBottomOperationScrollToBottom) return;
      this.$refs.bottomOperationRef.closeAll();
    },
    // 滚动到底部
    scrolltolower: function scrolltolower() {
      if (this.history.allLoaded) return;
      console.log('触底');
      this.loadHistoryMessage();
    },
    // 滚动到顶部
    scrolltoupper: function scrolltoupper() {
      if (this.loading || this.loadend) return;
      console.log('滚动到顶部，加载更多历史消息');
      // 获取第一条消息的ID作为分页参数
      var firstMessage = this.list[0];
      if (firstMessage) {
        this.loadData(firstMessage.id);
      }
    },
    initScrollHeight: function initScrollHeight() {
      var _this15 = this;
      uni.createSelectorQuery().in(this).select('.scroll-view').boundingClientRect(function (data) {
        if (data) {
          _this15.scrollHeight = data.height;
        }
      }).exec();
    },
    // 获取内容高度
    initContentHeight: function initContentHeight() {
      var _this16 = this;
      uni.createSelectorQuery().in(this).select('.messageList_').boundingClientRect(function (data) {
        if (data) {
          var top = data.height - _this16.scrollHeight;
          console.log('🚀 ~ initContentHeight ~ top:', top);
          if (top > 0) {
            _this16.$nextTick(function () {
              _this16.scroll_top = top;
            });
          }
        }
      }).exec();
    },
    /**
     *  根据用户id获取用户昵称
     * @param userId 用户id
     * @returns 用户昵称
     */
    getByIdUser: function getByIdUser(userId) {
      var _this$userArr$find;
      return ((_this$userArr$find = this.userArr.find(function (user) {
        return user.userId === userId;
      })) === null || _this$userArr$find === void 0 ? void 0 : _this$userArr$find.nickname) || '';
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 80:
/*!*********************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& */ 81);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 81:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[74,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesHuYunIm/pages/chat/index.js.map