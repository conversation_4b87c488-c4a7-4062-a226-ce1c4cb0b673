{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?9590", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?39a2", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?4e48", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?19aa", "uni-app:///pagesHuYunIm/pages/index/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?b06e", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/index/index.vue?024b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userMap", "users", "defaultAvatar", "userInfo", "userId", "nickname", "channelCode", "avatar", "token", "onShow", "onLoad", "mqttClient", "onUnload", "onPullDownRefresh", "methods", "initMqtt", "mqttUserInfo", "callbacks", "onConnect", "console", "onMessage", "onReconnect", "onError", "onEnd", "handleMqttMessage", "chatMsg", "loadData", "res", "err", "userArr", "userItem", "<PERSON><PERSON>", "connect", "item", "uni", "url", "JSON", "fail", "formatTime", "hour", "minute", "hour12", "yesterday", "month", "day", "getMessagePreview", "refreshData", "title", "icon", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsD9uB;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA;AAAA,eAOA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC,OACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;EACA;EAEAC;IACA;IACAD;EACA;EAEAE;IACA;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC,+CACA,uBACA,iCACA,4BACA,sBACA,8CACA,MACA;kBAEAC;oBACAC;sBACAC;sBACA;oBACA;oBACAC;sBACA;oBACA;oBACAC;sBACAF;sBACA;oBACA;oBACAG;sBACAH;sBACA;oBACA;oBACAI;sBACAJ;sBACA;oBACA;kBACA,GAEA;kBACAR;gBACA;kBACAQ;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAK;MACA;QACA;;QAEA;QACA;UACAC;QACA;;QAEA;QACA;UACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;UACA;QACA;QAEAN;MACA;IACA;IAEA;AACA;AACA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;gBAAAC;gBAAAC;gBAEA;kBACA;kBACA;oBACAC;oBACA;sBACA;wBACAC;wBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA1B;sBACA;wBACAuB;sBACA;oBACA;;oBAEA;oBACA;sBACAA;oBACA;kBACA;;kBAEA;kBACAA;oBACA;oBACA;oBACA;kBACA;kBACA;kBACA;kBACAI;gBACA;gBAEA;kBACAZ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;AACA;AACA;IACAa;MACA;MACArB;MACA;MACAsB;MACA;MACAC;QACAC,0IACAC,8BACA;QACAC;UACAlB;QACA;MACA;IACA;IAEA;AACA;AACA;IACAmB;MACA;MAEA;MACA;MACA;;MAEA;MACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;MACAC;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAZ;kBACAa;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9B;gBACAe;kBACAa;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAf;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClUA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6441f5a4&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6441f5a4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6441f5a4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6441f5a4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.users, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = item.updateTime ? _vm.formatTime(item.updateTime) : null\n    var m1 =\n      item.lastMsg && item.lastMsg.msgType == \"text\"\n        ? _vm.getMessagePreview(item.lastMsg)\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.users.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 会话列表 -->\r\n    <scroll-view class=\"chat-list\" scroll-y=\"true\" :show-scrollbar=\"false\">\r\n      <view class=\"list-item\" v-for=\"(item, index) in users\" :key=\"index\" @click=\"connect(item)\">\r\n        <view class=\"avatar-container\">\r\n          <!-- 未读消息红点 -->\r\n          <view class=\"unread-badge\" v-if=\"item.notReadNum > 0\">\r\n            <text class=\"unread-count\" v-if=\"item.notReadNum <= 99\">{{ item.notReadNum }}</text>\r\n            <text class=\"unread-count\" v-else>99+</text>\r\n          </view>\r\n          <image class=\"avatar\" :src=\"item.avatar || defaultAvatar\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <view class=\"content\">\r\n          <view class=\"title-row\">\r\n            <text class=\"name\">{{ item.title }}</text>\r\n            <text class=\"time\" v-if=\"item.updateTime\">{{ formatTime(item.updateTime) }}</text>\r\n          </view>\r\n          <view class=\"message-row\">\r\n            <view class=\"last-message\">\r\n              <!-- 文本消息 -->\r\n              <text v-if=\"item.lastMsg && item.lastMsg.msgType == 'text'\" class=\"message-text\">\r\n                {{ getMessagePreview(item.lastMsg) }}\r\n              </text>\r\n              <!-- 图片消息 -->\r\n              <text v-else-if=\"item.lastMsg && item.lastMsg.msgType == 'image'\" class=\"message-text\">[图片]</text>\r\n              <!-- 语音消息 -->\r\n              <text v-else-if=\"item.lastMsg && item.lastMsg.msgType == 'voice'\" class=\"message-text\">[语音]</text>\r\n              <!-- 视频消息 -->\r\n              <text v-else-if=\"item.lastMsg && item.lastMsg.msgType == 'video'\" class=\"message-text\">[视频]</text>\r\n              <!-- 文件消息 -->\r\n              <text v-else-if=\"item.lastMsg && item.lastMsg.msgType == 'file'\" class=\"message-text\">[文件]</text>\r\n              <!-- 默认消息 -->\r\n              <text v-else class=\"message-text placeholder\">暂无消息</text>\r\n            </view>\r\n            <!-- 消息状态指示器 -->\r\n            <view class=\"message-status\" v-if=\"item.lastMsg\">\r\n              <text class=\"mute-icon\" v-if=\"item.isMuted\">🔕</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-if=\"users.length === 0\">\r\n        <text class=\"empty-icon\">💬</text>\r\n        <text class=\"empty-text\">暂无会话</text>\r\n        <text class=\"empty-desc\">开始一段新的对话吧</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n// 导入新的MQTT工具包\r\nimport mqttClient from '../../utils/mqttClient.js'\r\nimport { createUserInfo, MESSAGE_TYPES } from '../../utils/mqttConfig.js'\r\nimport mqtt from '../../lib/mqtt.min.js'\r\nimport { listUser } from '../../api/public.js'\r\nimport Cache from '../../utils/cache.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userMap: {},\r\n      users: [],\r\n      defaultAvatar: 'https://dummyimage.com/100x100/cccccc/ffffff?text=头像',\r\n      userInfo: {\r\n        userId: '1921822887908581377',\r\n        nickname: '范发发',\r\n        channelCode: 'hbs119',\r\n        avatar: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain',\r\n        token:\r\n          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTE0NTg4MX0.oTkYdEi7Ewms2SxOfRP3uKaMUz9YzOTDtQDIpZeG0_8'\r\n      }\r\n    }\r\n  },\r\n\r\n  onShow() {\r\n    this.loadData()\r\n    this.initMqtt()\r\n  },\r\n\r\n  onLoad() {\r\n    // 设置MQTT库\r\n    mqttClient.setMqttLib(mqtt)\r\n  },\r\n\r\n  onUnload() {\r\n    // 页面卸载时断开MQTT连接\r\n    mqttClient.disconnect()\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    // 下拉刷新\r\n    this.refreshData()\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 初始化MQTT连接\r\n     */\r\n    async initMqtt() {\r\n      try {\r\n        // 创建用户信息对象\r\n        const mqttUserInfo = createUserInfo(\r\n          this.userInfo.userId,\r\n          this.userInfo.nickname || '用户',\r\n          this.userInfo.channelCode,\r\n          this.userInfo.token,\r\n          this.userInfo.avatar || this.defaultAvatar,\r\n          'DEV'\r\n        )\r\n\r\n        const callbacks = {\r\n          onConnect: () => {\r\n            console.log('MQTT连接成功')\r\n            this.isConnected = true\r\n          },\r\n          onMessage: (topic, mqttMsg) => {\r\n            this.handleMqttMessage(mqttMsg)\r\n          },\r\n          onReconnect: () => {\r\n            console.log('MQTT重连中...')\r\n            this.isConnected = false\r\n          },\r\n          onError: (error) => {\r\n            console.error('MQTT连接错误:', error)\r\n            this.isConnected = false\r\n          },\r\n          onEnd: () => {\r\n            console.log('MQTT连接已断开')\r\n            this.isConnected = false\r\n          }\r\n        }\r\n\r\n        // 连接MQTT\r\n        mqttClient.connect(mqttUserInfo, callbacks)\r\n      } catch (error) {\r\n        console.error('初始化MQTT失败:', error)\r\n        this.isConnected = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 处理MQTT消息\r\n     */\r\n    handleMqttMessage(mqttMsg) {\r\n      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {\r\n        const chatMsg = mqttMsg.data\r\n\r\n        // 设置用户昵称\r\n        if (this.userMap.hasOwnProperty(chatMsg.userId)) {\r\n          chatMsg.nickname = this.userMap[chatMsg.userId].nickname\r\n        }\r\n\r\n        // 更新用户列表中的消息信息\r\n        for (let i = 0; i < this.users.length; i++) {\r\n          if (this.users[i].id === chatMsg.groupId) {\r\n            this.users[i].lastMsg = chatMsg\r\n            this.users[i].updateTime = chatMsg.createTime\r\n            this.users[i].notReadNum = (this.users[i].notReadNum || 0) + 1\r\n\r\n            // 将更新的会话移到顶部\r\n            const updatedUser = this.users.splice(i, 1)[0]\r\n            this.users.unshift(updatedUser)\r\n            break\r\n          }\r\n        }\r\n\r\n        console.log('收到聊天消息:', chatMsg)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载用户数据\r\n     */\r\n    async loadData() {\r\n      try {\r\n        const [res, err] = await listUser({})\r\n\r\n        if (res) {\r\n          // 构建用户映射表\r\n          for (let i = 0; i < res.length; i++) {\r\n            let userArr = res[i].userArr\r\n            if (userArr) {\r\n              for (let j = 0; j < userArr.length; j++) {\r\n                let userItem = userArr[j]\r\n                this.userMap[userItem.userId] = userItem\r\n              }\r\n            }\r\n\r\n            // 设置最后一条消息的昵称\r\n            if (res[i].lastMsg && res[i].lastMsg.userId) {\r\n              const userId = res[i].lastMsg.userId\r\n              if (this.userMap[userId]) {\r\n                res[i].lastMsg.nickname = this.userMap[userId].nickname\r\n              }\r\n            }\r\n\r\n            // 初始化未读消息数\r\n            if (!res[i].notReadNum) {\r\n              res[i].notReadNum = 0\r\n            }\r\n          }\r\n\r\n          // 按更新时间排序\r\n          res.sort((a, b) => {\r\n            const timeA = a.updateTime || 0\r\n            const timeB = b.updateTime || 0\r\n            return timeB - timeA\r\n          })\r\n          this.users = res\r\n          // 缓存用户映射表\r\n          Cache.set('userMap', JSON.stringify(this.userMap))\r\n        }\r\n\r\n        if (err) {\r\n          console.error('加载用户数据失败:', err)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载数据异常:', error)\r\n      }\r\n    },\r\n    /**\r\n     * 点击会话项\r\n     */\r\n    connect(item) {\r\n      // 断开当前MQTT连接\r\n      mqttClient.disconnect()\r\n      // 清除未读消息数\r\n      item.notReadNum = 0\r\n      // 跳转到聊天页面\r\n      uni.navigateTo({\r\n        url: `/pagesHuYunIm/pages/chat/index?groupInfo=${encodeURIComponent(JSON.stringify(item))}&userInfo=${encodeURIComponent(\r\n          JSON.stringify(this.userInfo)\r\n        )}`,\r\n        fail: (err) => {\r\n          console.error('跳转失败:', err)\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 格式化时间显示\r\n     */\r\n    formatTime(timestamp) {\r\n      if (!timestamp) return ''\r\n\r\n      const now = new Date()\r\n      const msgTime = new Date(timestamp)\r\n      const diff = now.getTime() - msgTime.getTime()\r\n\r\n      // 今天\r\n      if (now.toDateString() === msgTime.toDateString()) {\r\n        return msgTime.toLocaleTimeString('zh-CN', {\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          hour12: false\r\n        })\r\n      }\r\n\r\n      // 昨天\r\n      const yesterday = new Date(now)\r\n      yesterday.setDate(yesterday.getDate() - 1)\r\n      if (yesterday.toDateString() === msgTime.toDateString()) {\r\n        return '昨天'\r\n      }\r\n\r\n      // 一周内\r\n      if (diff < 7 * 24 * 60 * 60 * 1000) {\r\n        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n        return weekdays[msgTime.getDay()]\r\n      }\r\n\r\n      // 更早\r\n      return msgTime.toLocaleDateString('zh-CN', {\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取消息预览文本\r\n     */\r\n    getMessagePreview(lastMsg) {\r\n      if (!lastMsg) return ''\r\n      const info = JSON.parse(lastMsg.content)\r\n      const nickname = lastMsg.nickname || '未知用户'\r\n      const content = info.msg || ''\r\n      // 限制显示长度\r\n      const maxLength = 30\r\n      const preview = `${nickname}: ${content}`\r\n      if (preview.length > maxLength) {\r\n        return preview.substring(0, maxLength) + '...'\r\n      }\r\n      return preview\r\n    },\r\n\r\n    /**\r\n     * 刷新数据\r\n     */\r\n    async refreshData() {\r\n      try {\r\n        await this.loadData()\r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'success',\r\n          duration: 1500\r\n        })\r\n      } catch (error) {\r\n        console.error('刷新失败:', error)\r\n        uni.showToast({\r\n          title: '刷新失败',\r\n          icon: 'none',\r\n          duration: 1500\r\n        })\r\n      } finally {\r\n        // 停止下拉刷新\r\n        uni.stopPullDownRefresh()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  height: 100vh;\r\n  background-color: #f7f7f7;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n/* 会话列表样式 */\r\n.chat-list {\r\n  flex: 1;\r\n  background-color: #ffffff;\r\n}\r\n.list-item {\r\n  display: flex;\r\n  padding: 24rpx 32rpx;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background-color: #ffffff;\r\n  transition: background-color 0.2s;\r\n\r\n  &:active {\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n/* 头像容器样式 */\r\n.avatar-container {\r\n  position: relative;\r\n  margin-right: 24rpx;\r\n\r\n  .avatar {\r\n    width: 96rpx;\r\n    height: 96rpx;\r\n    border-radius: 12rpx;\r\n    background-color: #f0f0f0;\r\n  }\r\n\r\n  .unread-badge {\r\n    position: absolute;\r\n    top: -8rpx;\r\n    right: -8rpx;\r\n    background-color: #ff4444;\r\n    border-radius: 20rpx;\r\n    min-width: 32rpx;\r\n    height: 32rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: 2px solid #ffffff;\r\n\r\n    .unread-count {\r\n      color: #ffffff;\r\n      font-size: 20rpx;\r\n      font-weight: bold;\r\n      line-height: 1;\r\n      padding: 0 8rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  min-width: 0;\r\n}\r\n\r\n.title-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n\r\n  .name {\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .time {\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n    margin-left: 16rpx;\r\n    flex-shrink: 0;\r\n  }\r\n}\r\n\r\n.message-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n\r\n  .last-message {\r\n    flex: 1;\r\n    min-width: 0;\r\n\r\n    .message-text {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      line-height: 1.4;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n\r\n      &.placeholder {\r\n        color: #cccccc;\r\n        font-style: italic;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-status {\r\n    margin-left: 16rpx;\r\n\r\n    .mute-icon {\r\n      font-size: 24rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 60rpx;\r\n\r\n  .empty-icon {\r\n    font-size: 120rpx;\r\n    margin-bottom: 32rpx;\r\n    opacity: 0.3;\r\n  }\r\n\r\n  .empty-text {\r\n    font-size: 32rpx;\r\n    color: #666666;\r\n    margin-bottom: 16rpx;\r\n  }\r\n\r\n  .empty-desc {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n/* 连接状态指示器 */\r\n.connection-status {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  color: #ffffff;\r\n  padding: 16rpx 32rpx;\r\n  border-radius: 8rpx;\r\n  z-index: 1000;\r\n\r\n  .status-text {\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n/* 响应式适配 */\r\n@media screen and (max-width: 750rpx) {\r\n  .list-item {\r\n    padding: 20rpx 24rpx;\r\n  }\r\n\r\n  .avatar-container {\r\n    margin-right: 20rpx;\r\n\r\n    .avatar {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n    }\r\n  }\r\n\r\n  .title-row .name {\r\n    font-size: 30rpx;\r\n  }\r\n\r\n  .message-row .message-text {\r\n    font-size: 26rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6441f5a4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6441f5a4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137555145\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}