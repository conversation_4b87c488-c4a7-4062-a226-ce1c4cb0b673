# 群聊滚动位置记录功能测试

## 功能说明
为每个群聊记录用户滑动的距离（scroll_top），在再次进入时恢复到之前的位置。

## 实现的功能

### 1. 保存滚动位置
- 在页面卸载时（onUnload）保存当前滚动位置
- 在滚动过程中实时保存（使用节流，每500ms最多保存一次）
- 使用 `uni.getStorageSync('group_scroll_positions')` 存储所有群聊的滚动位置

### 2. 恢复滚动位置
- 在页面加载时（onLoad）恢复之前保存的滚动位置
- 在消息加载完成后，如果有保存的位置则恢复，否则滚动到底部

### 3. 数据结构
```javascript
// 存储格式
{
  "group_scroll_positions": {
    "群聊ID1": 滚动位置数值,
    "群聊ID2": 滚动位置数值,
    ...
  }
}
```

## 测试步骤

1. **进入群聊A**
   - 滚动到某个位置
   - 退出群聊
   - 检查控制台是否有保存日志

2. **再次进入群聊A**
   - 检查是否恢复到之前的滚动位置
   - 检查控制台是否有恢复日志

3. **进入不同群聊B**
   - 滚动到不同位置
   - 退出群聊
   - 再次进入，检查是否恢复正确位置

4. **验证独立性**
   - 群聊A和群聊B的滚动位置应该独立保存和恢复

## 关键代码位置

### 保存方法
```javascript
saveScrollPosition() {
  // 保存当前群聊的滚动位置到本地存储
}
```

### 恢复方法
```javascript
restoreScrollPosition() {
  // 从本地存储恢复当前群聊的滚动位置
}
```

### 节流保存
```javascript
throttledSaveScrollPosition: throttle(function() {
  this.saveScrollPosition()
}, 500)
```

## 注意事项

1. 使用节流避免频繁保存影响性能
2. 延迟恢复滚动位置，确保页面内容已渲染
3. 错误处理，避免存储失败影响正常功能
4. 只有滚动位置大于0时才恢复，避免无意义的恢复

## 使用方法

### 1. 在现有项目中使用
已经在 `pagesHuYunIm/pages/chat/index.vue` 中实现了完整功能，无需额外配置。

### 2. 演示页面
可以使用 `scroll-position-demo.vue` 文件来测试功能：
- 显示所有群聊及其保存的滚动位置
- 提供清除所有滚动位置的功能
- 显示本地存储的详细信息

### 3. 调试信息
在浏览器控制台中可以看到以下日志：
- `保存群聊 {groupId} 的滚动位置: {position}`
- `恢复群聊 {groupId} 的滚动位置: {position}`

### 4. 手动操作存储
```javascript
// 获取所有滚动位置
const positions = uni.getStorageSync('group_scroll_positions')

// 清除所有滚动位置
uni.removeStorageSync('group_scroll_positions')

// 设置特定群聊的滚动位置
const positions = uni.getStorageSync('group_scroll_positions') || {}
positions['群聊ID'] = 1000
uni.setStorageSync('group_scroll_positions', positions)
```

## 技术实现细节

### 存储机制
- 使用 `uni.getStorageSync` 和 `uni.setStorageSync` 进行本地存储
- 数据持久化，应用重启后仍然有效
- 支持多个群聊独立存储

### 性能优化
- 使用 `throttle` 函数限制保存频率（500ms）
- 延迟恢复避免与页面渲染冲突
- 错误处理确保功能稳定性

### 兼容性
- 支持 uni-app 所有平台（H5、小程序、App）
- 使用标准的 uni-app API，无第三方依赖
