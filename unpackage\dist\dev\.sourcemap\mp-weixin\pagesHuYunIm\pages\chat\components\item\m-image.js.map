{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?c6f7", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?0994", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?a5bf", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?f67f", "uni-app:///pagesGoEasy/chat_page/components/item/m-image.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?d142", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-image.vue?30a3"], "names": ["props", "isMy", "type", "default", "value", "data", "computed", "methods", "getImageHeight", "height", "imgLoad"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCQ9wB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QAAAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAGAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAq7C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAz8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/m-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-image.vue?vue&type=template&id=52979618&scoped=true&\"\nvar renderjs\nimport script from \"./m-image.vue?vue&type=script&lang=js&\"\nexport * from \"./m-image.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-image.vue?vue&type=style&index=0&id=52979618&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52979618\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/m-image.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-image.vue?vue&type=template&id=52979618&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getImageHeight(_vm.value.payload)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-image.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_r m-image\" :style=\"{ height: getImageHeight(value.payload) + 'rpx' }\">\n\t\t<!-- <image class=\"img\" :src=\"value.payload.thumbnail || value.payload.url\" mode=\"widthFix\"></image> -->\n\t\t<image class=\"img\" :src=\"value.payload.url\" mode=\"heightFix\" @load=\"imgLoad\"></image>\n\t\t<!-- {{value.payload.thumbnail}} -->\n\t</view>\n</template>\n<script>\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcomputed: {},\n\tmethods: {\n\t\t/**\n\t\t * 核心就是设置高度，产生明确占位\n\t\t *\n\t\t * 小  (宽度和高度都小于预设尺寸)\n\t\t *    设高=原始高度\n\t\t * 宽 (宽度>高度)\n\t\t *    高度= 根据宽度等比缩放\n\t\t * 窄  (宽度<高度)或方(宽度=高度)\n\t\t *    设高=MAX height\n\t\t *\n\t\t * @param width,height\n\t\t * @returns number\n\t\t */\n\t\tgetImageHeight(payload) {\n\t\t\tconst { width, height } = payload;\r\n\t\t\tconst IMAGE_MAX_WIDTH = 130;\r\n\t\t\tconst IMAGE_MAX_HEIGHT = 94;\n\t\t\tif (width < IMAGE_MAX_WIDTH && height < IMAGE_MAX_HEIGHT) {\n\t\t\t\treturn height * 3;\n\t\t\t} else if (width > height) {\n\t\t\t\treturn (IMAGE_MAX_WIDTH / width) * height * 3;\n\t\t\t} else if (width === height || width < height) {\n\t\t\t\treturn IMAGE_MAX_HEIGHT * 3;\n\t\t\t}\n\t\t},\n\t\t\n\t\t\n\t\timgLoad(e){\n\t\t\tthis.$emit('imgLoad',e)\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.m-image {\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\t.img {\n\t\tmin-height: 100rpx;\n\t\tbackground-color: #fff;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-image.vue?vue&type=style&index=0&id=52979618&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-image.vue?vue&type=style&index=0&id=52979618&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755138624565\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}