<template>
  <view :id="page_font_size">
    <view style="height: 30rpx"></view>
    <view class="flex_r list">
      <view
        class="flex_c_c nowrap_ item"
        v-for="(item, index) in list"
        :key="item.member_id"
        @click="to('/pagesGoEasy/group_member_infor/index', { member_id: item.member_id, group_id })"
      >
        <view class="item-img">
          <image class="img" :src="item.avatar" mode="aspectFill"></image>
        </view>
        <view class="text_24 icon_ nowrap_ item-title">
          <text class="nowrap_">{{ item.name }}</text>
        </view>
      </view>
      <template v-if="invite_status">
        <view class="flex_c_c item" @click="remove_member">
          <view class="item-img icon_ item_img">
            <image
              class="img"
              src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTEyOCA0ODBoNzY4YzE3LjY4IDAgMzIgMTQuMzM2IDMyIDMyIDAgMTcuNjgtMTQuMzIgMzItMzIgMzJIMTI4Yy0xNy42OCAwLTMyLTE0LjMyLTMyLTMyIDAtMTcuNjY0IDE0LjMyLTMyIDMyLTMyeiIgZmlsbD0iI2IyYjJiMiIvPjwvc3ZnPg=="
              mode="aspectFill"
            ></image>
          </view>
          <view class="text_28 item-title" style="height: 30rpx"></view>
        </view>
        <view class="flex_c_c item" @click="to(`/pagesGoEasy/admin/add_member?group_id=${group_id}`)">
          <view class="item-img icon_ item_img">
            <image
              class="img"
              src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTkyOCA1NDRINTQ0djM4NGMwIDE3LjY4LTE0LjMyIDMyLTMyIDMycy0zMi0xNC4zMi0zMi0zMlY1NDRIOTZjLTE3LjY4IDAtMzItMTQuMzItMzItMzJzMTQuMzItMzIgMzItMzJoMzg0Vjk2YzAtMTcuNjggMTQuMzItMzIgMzItMzJzMzIgMTQuMzIgMzIgMzJ2Mzg0aDM4NGMxNy42OCAwIDMyIDE0LjMyIDMyIDMycy0xNC4zMiAzMi0zMiAzMnoiIGZpbGw9IiNiMmIyYjIiLz48L3N2Zz4="
              mode="aspectFill"
            ></image>
          </view>
          <view class="text_28 item-title" style="height: 30rpx"></view>
        </view>
      </template>
    </view>
    <view class="icon_ text_26 color__ showAll" @click="showAll" v-if="tooMuch">
      <text>查看更多群成员</text>
      <view class="icon_ showAll-icon">
        <image
          class="img"
          src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
          mode="aspectFill"
        ></image>
      </view>
    </view>

    <view class="interval"></view>

    <!-- 群聊名称 -->
    <view class="list-option">
      <view class="flex_r fa_c item" @click="toGroupName('/pagesGoEasy/group_name_edit/index', invite_status)">
        <view class="text_30 bold_ item-title">群聊名称</view>
        <view class="flex1"></view>
        <view class="text_30 color__ item-subtitle">群名称</view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>
    <view class="list-option">
      <view class="flex_r fa_c item" @click="toNickname('/pagesGoEasy/group_nickname_edit/index')">
        <view class="text_30 bold_ item-title">我在本群的昵称</view>
        <view class="flex1"></view>
        <view class="text_30 color__ item-subtitle">xxxxx</view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>

    <!-- 群二维码 -->
    <view class="list-option">
      <view class="flex_r fa_c item" @click="to(`/pagesGoEasy/group_code/index?group_id=${group_id}`)">
        <view class="text_30 bold_ item-title">群二维码</view>
        <view class="flex1"></view>
        <view class="item-subtitle">
          <image
            class="img"
            src="data:image/svg+xml;base64,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"
            mode="aspectFill"
          ></image>
        </view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>
    <view class="interval"></view>
    <view class="list-option">
      <view class="flex_r fa_c item" @click="to(`/pagesGoEasy/group_history_get/index?group_id=${group_id}`)">
        <view class="text_30 bold_ item-title">查找记录</view>
        <view class="flex1"></view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>

    <view class="list-option">
      <view class="flex_r fa_c item" @click="to(`/pagesGoEasy/group_report/index?group_id=${group_id}`)">
        <view class="text_30 bold_ item-title">投诉/举报</view>
        <view class="flex1"></view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>
    <view class="list-option">
      <view class="flex_r fa_c item" @click="to(`/pagesGoEasy/group_set_font_size/index?group_id=${group_id}`)">
        <view class="text_30 bold_ item-title">设置聊天区字体大小</view>
        <view class="flex1"></view>
        <view class="icon_ item-enter">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>

    <view class="interval"></view>

    <!-- 群公告 -->
    <view class="list-option" @click="toAnnouncemen">
      <view class="flex_r fa_c item">
        <view class="text_30 bold_ item-title">群公告</view>
        <view class="flex1"></view>
        <view class="icon_ item-enter" v-if="invite_status">
          <image
            class="img"
            src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <view class="color__ text_26 list-option-text">
        <view class="text_30" :style="{ whiteSpace: 'pre-wrap' }" v-html="renderTextMessage"></view>
      </view>
      <m-line color="#dedede" length="100%" :hairline="true"></m-line>
    </view>

    <m-bottom-paceholder></m-bottom-paceholder>
    <!-- 看全部用户 -->
    <member-selection-loading
      :title="`全部成员(${count})`"
      ref="memberSelectionRefArr"
      :group_id="group_id"
      @itemclick="itemclickAll"
    ></member-selection-loading>
    <!-- 移除用户 -->
    <member-selection-loading
      title="移除群成员"
      ref="memberSelectionLoadingRef"
      :group_id="group_id"
      @itemclick="itemclick"
    ></member-selection-loading>
  </view>
</template>

<script>
import memberSelectionLoading from '../../components/memberSelectionLoading/index'
import { EmojiDecoder, emojiMap } from '../../lib/EmojiDecoder.js'
import { mapState } from 'vuex'
const emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/'
const decoder = new EmojiDecoder(emojiUrl, emojiMap)
let group_id = null
let showAll = false
export default {
  components: {
    memberSelectionLoading
  },
  data() {
    return {
      tooMuch: false, //人数是否超过25
      group_id: null,
      alllist: [],
      list: [],
      count: '--',
      group_to: {},
      memberInfo: {
        member_id: '1',
        avatar: '',
        name: '李大头'
      }, //昵称
      noticeContent: '',
      allow_modify_nickname: 0,
      invite_status: null //是否可拉人或踢人
    }
  },
  onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    showAll = false
    group_id = `${e.group_id}`
    this.group_id = group_id
    // 获取群成员
    this.getData()
    // 群信息
    this.getGroupInfr()
    // 群公告
    this.getNotice()
    // 群个性化内容
    this.getGroupMemberInfo()
  },
  computed: mapState({
    page_font_size: (state) => state.page_font_size,
    //渲染文本消息，如果包含表情，替换为图片
    //todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现
    renderTextMessage() {
      if (!this.noticeContent) return '暂无群公告'
      return '<span>' + decoder.decode(this.noticeContent) + '</span>'
    }
  }),
  methods: {
    async showAll() {
      this.$refs.memberSelectionRefArr.open()
    },
    // 修改公告
    toAnnouncemen() {
      if (!this.invite_status) return
      uni.$off('getNotice', this.getNotice)
      uni.$on('getNotice', this.getNotice)
      to(`/pagesGoEasy/group_announcement_add/index`, { group_id, text: this.noticeContent, group_to: this.group_to })
    },
    // 修改群名
    toGroupName(url, invite_status = true) {
      if (!invite_status) return
      uni.$off('getGroupInfr', this.getGroupInfr)
      uni.$on('getGroupInfr', this.getGroupInfr)
      to(`${url}`, { group_id, group_to: this.group_to })
    },
    // 群昵称
    toNickname(url) {
      uni.$off('getGroupMemberInfo', this.getGroupMemberInfo)
      uni.$on('getGroupMemberInfo', this.getGroupMemberInfo)
      to(`${url}`, { group_id, ...this.memberInfo })
    },
    // 获取群公告
    async getNotice() {},
    // 获取个性化
    async getGroupMemberInfo() {},

    // 获取群名等信息
    async getGroupInfr() {
      const res = await this.API_info()
      if (res) {
        const data = res.data
        this.allow_modify_nickname = data.allow_modify_nickname
        // console.log(allow_modify_nickname)
        this.group_to = {
          id: data.id,
          type: this.GoEasy.IM_SCENE.GROUP,
          data: {
            name: data.name,
            avatar: data.avatar
          }
        }
      }
    },
    async getData() {
      uni.showLoading({
        title: '加载中...'
      })

      uni.setNavigationBarTitle({
        title: `聊天信息(5)`
      })
      this.count = 5
      this.invite_status = true
      let list = [
        {
          member_id: '1',
          avatar: '',
          name: '李大头'
        },
        {
          member_id: '2',
          avatar: '',
          name: '张小头'
        },
        {
          member_id: '3',
          avatar: '',
          name: '黄中头'
        },
        {
          member_id: '4',
          avatar: '',
          name: '吴滑头'
        },
        {
          member_id: '5',
          avatar: '',
          name: '很头大'
        },
        {
          member_id: '6',
          avatar: '',
          name: '很长的名字很长的名字'
        }
      ]
      if (list.length > 25) {
        this.tooMuch = true
      }
      // 管理员
      if (this.invite_status) {
        this.list = list.slice(0, 23)
      } else {
        this.list = list.slice(0, 25)
      }
      this.alllist = list

      uni.hideLoading()
    },
    remove_member() {
      this.$refs.memberSelectionLoadingRef.open()
    },
    itemclickAll(item) {
      to('/pagesGoEasy/group_member_infor/index', { member_id: item.member_id, group_id })
    },
    itemclick(item) {
      uni.showModal({
        content: `确定将 ${item.name} 移出群聊？`,
        success: async (e) => {
          if (e.confirm) {
            this.list = this.list.filter((im) => {
              return item.member_id != im.member_id
            })
          } else if (e.cancel) {
          }
        }
      })
    },
    // 获取群成员
    API_groupMember(page = 1, limit = 30) {},
    // 踢出群
    API_kick(member_id) {},
    // 获取公告
    API_notice() {},
    // 获取个性化内容
    API_groupMemberInfo() {},
    // 获取群消息
    API_info() {}
  }
}
</script>

<style lang="scss" scoped>
.interval {
  width: 100%;
  height: 20rpx;
  background-color: #ededed;
}
.list {
  width: calc(100% - 40rpx);
  flex-wrap: wrap;
  margin: 0 auto 0 10rpx;
  .item {
    width: calc(20% - 20rpx);
    margin-left: 20rpx;
    margin-bottom: 30rpx;
    .item-img {
      box-sizing: border-box;
      width: 100rpx;
      height: 100rpx;
      border-radius: 10rpx;
      overflow: hidden;
      border: 1px solid #efefef;
    }
    .item-title {
      width: 100%;
      color: #7f7f7f;
      margin-top: 4rpx;
    }
    .item_img {
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 12rpx;
      border: 2px dashed #cacaca;
      .img {
        width: 50%;
        height: 50%;
      }
    }
  }
}
.showAll {
  width: 100%;
  height: 70rpx;
  margin-bottom: 20rpx;
  .showAll-icon {
    width: 34rpx;
    height: 34rpx;
  }
}

.list-option {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 0 30rpx;
  .item {
    box-sizing: border-box;
    padding-right: 20rpx;
    width: 100%;
    height: 100rpx;
    .item-title {
    }
    .item-subtitle {
      height: 40rpx;
      line-height: 40rpx;
      .img {
        width: 40rpx;
        height: 40rpx;
      }
    }
    .item-enter {
      width: 34rpx;
      height: 34rpx;
      margin-top: 4rpx;
      margin-left: 10rpx;
    }
  }

  .list-option-text {
    position: relative;
    top: -20rpx;
    width: 100%;
    box-sizing: border-box;
    padding-right: 20rpx;
  }
}
</style>
