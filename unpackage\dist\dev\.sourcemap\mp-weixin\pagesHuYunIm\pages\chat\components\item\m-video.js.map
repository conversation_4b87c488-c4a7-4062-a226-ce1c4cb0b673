{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?0a98", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?332d", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?c4bd", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?2971", "uni-app:///pagesGoEasy/chat_page/components/item/m-video.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?3864", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesHuYunIm/pages/chat/components/item/m-video.vue?0064"], "names": ["props", "isMy", "type", "default", "value", "data", "computed", "getImageHeight", "width", "height", "getTimes", "h", "m", "s", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCe9wB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MACA;QAAAC;QAAAC;MACA;QACAD;QACAC;QACA;UACAD;UACAC;UACA;YACAD;YACAC;UACA;QACA;MACA;MACA;QACAD;QACAC;MACA;MACA;QACAD;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAC;MACA;MACA;IACA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAq7C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAz8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/m-video.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-video.vue?vue&type=template&id=533b1d90&scoped=true&\"\nvar renderjs\nimport script from \"./m-video.vue?vue&type=script&lang=js&\"\nexport * from \"./m-video.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-video.vue?vue&type=style&index=0&id=533b1d90&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"533b1d90\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/m-video.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-video.vue?vue&type=template&id=533b1d90&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-video.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-video.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_r m-video\">\r\n\t\t<!-- heightFix -->\n\t\t<image class=\"z_index2\" :style=\"getImageHeight\" :src=\"value.payload.thumbnail.url\" mode=\"aspectFill\"></image>\n\t\t<view class=\"m-video-icon\">\n\t\t\t<image\n\t\t\t\tclass=\"img\"\n\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmk1LjU1YjUzYTgxcDB1a0U0IiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxNi42NTUgOS4zMUMyMzcuMzgyIDkuMzEgMTAuODYgMjM1LjgzIDEwLjg2IDUxNS4xMDJzMjI2LjUyIDUwNS43OTQgNTA1Ljc5NCA1MDUuNzk0IDUwNS43OTMtMjI2LjUyMSA1MDUuNzkzLTUwNS43OTRjMC0yNzcuNzIxLTIyNi41Mi01MDUuNzk0LTUwNS43OTMtNTA1Ljc5NHptMCA5ODIuMTA4Yy0yNjIuMjA3IDAtNDc0Ljc2NC0yMTIuNTU3LTQ3NC43NjQtNDc0Ljc2MyAwLTI2Mi4yMDcgMjEyLjU1Ny00NzQuNzY0IDQ3NC43NjQtNDc0Ljc2NCAyNjIuMjA2IDAgNDc0Ljc2MyAyMTIuNTU3IDQ3NC43NjMgNDc0Ljc2NCAwIDI2Mi4yMDYtMjEyLjU1NyA0NzQuNzYzLTQ3NC43NjMgNDc0Ljc2M3oiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTcuNTViNTNhODFwMHVrRTQiIGNsYXNzPSJzZWxlY3RlZCIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik03MDUuOTQgNDc0Ljc2NEw0NzAuMTEgMzI1LjgxOGMtMjEuNzIyLTEzLjk2My01MS4yLTcuNzU3LTYzLjYxMyAxMy45NjQtNC42NTUgNy43NTctNy43NTggMTUuNTE1LTcuNzU4IDI0LjgyNHYyOTcuODkxYzAgMjYuMzc2IDIwLjE3IDQ2LjU0NSA0Ni41NDYgNDYuNTQ1IDkuMzA5IDAgMTcuMDY3LTMuMTAzIDI0LjgyNC03Ljc1N2wyMzcuMzgyLTE0OC45NDZjMjEuNzIxLTEzLjk2MyAyNy45MjctNDEuODkgMTMuOTY0LTYzLjYxMi00LjY1NS00LjY1NC05LjMxLTkuMzA5LTE1LjUxNi0xMy45NjN6IiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmk2LjU1YjUzYTgxcDB1a0U0IiBjbGFzcz0ic2VsZWN0ZWQiIGZpbGw9IiNmZmYiLz48L3N2Zz4=\"\n\t\t\t\tmode=\"aspectFill\"\n\t\t\t></image>\n\t\t</view>\n\t\t<view class=\"size_white text_22 m-video-time\">{{ getTimes }}</view>\n\t</view>\n</template>\n<script>\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [<PERSON><PERSON><PERSON>, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcomputed: {\n\t\tgetImageHeight() {\n\t\t\tlet { width, height } = this.value.payload.thumbnail;\n\t\t\tif (width < 300) {\n\t\t\t\twidth = width * 1.1;\n\t\t\t\theight = height * 1.1;\n\t\t\t\tif (width < 200) {\n\t\t\t\t\twidth = width * 1.2;\n\t\t\t\t\theight = height * 1.2;\n\t\t\t\t\tif (width < 150) {\n\t\t\t\t\t\twidth = width * 1.3;\n\t\t\t\t\t\theight = height * 1.3;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (width >= 360) {\n\t\t\t\twidth = width * 0.9;\n\t\t\t\theight = height * 0.9;\n\t\t\t}\n\t\t\treturn {\n\t\t\t\twidth: `${width}rpx`,\n\t\t\t\theight: `${height}rpx`\n\t\t\t};\n\t\t},\n\t\tgetTimes() {\n\t\t\tconst t = this.value.payload.video.duration;\n\t\t\tlet h = parseInt((t / 60 / 60) % 24);\n\t\t\tlet m = parseInt((t / 60) % 60);\n\t\t\tlet s = parseInt(t % 60);\n\t\t\th = h < 10 ? '0' + h : h;\n\t\t\tm = m < 10 ? '0' + m : m;\n\t\t\ts = s < 10 ? '0' + s : s;\n\t\t\tif (h === '00') return `${m}:${s}`;\n\t\t\treturn `${h}:${m}:${s}`;\n\t\t}\n\t},\n\tmethods: {}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.m-video {\n\tposition: relative;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tbackground-color: #fff;\n\t.m-video-icon {\n\t\tposition: absolute;\n\t\tz-index: 3;\n\t\twidth: 90rpx;\n\t\theight: 90rpx;\n\t\ttop: calc(50% - 45rpx);\n\t\tleft: calc(50% - 45rpx);\n\t\tborder-radius: 50%;\n\t\tbackground-color: rgba(000, 000, 000, 0.2);\n\t}\n\t.m-video-time {\n\t\tposition: absolute;\n\t\tz-index: 3;\n\t\tbottom: 10rpx;\n\t\tright: 10rpx;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-video.vue?vue&type=style&index=0&id=533b1d90&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-video.vue?vue&type=style&index=0&id=533b1d90&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755138624570\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}