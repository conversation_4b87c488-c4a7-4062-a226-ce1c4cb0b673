@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.interval.data-v-53459e40 {
  width: 100%;
  height: 20rpx;
  background-color: #ededed;
}
.list.data-v-53459e40 {
  width: calc(100% - 40rpx);
  flex-wrap: wrap;
  margin: 0 auto 0 10rpx;
}
.list .item.data-v-53459e40 {
  width: calc(20% - 20rpx);
  margin-left: 20rpx;
  margin-bottom: 30rpx;
}
.list .item .item-img.data-v-53459e40 {
  box-sizing: border-box;
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  overflow: hidden;
  border: 1px solid #efefef;
}
.list .item .item-title.data-v-53459e40 {
  width: 100%;
  color: #7f7f7f;
  margin-top: 4rpx;
}
.list .item .item_img.data-v-53459e40 {
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 12rpx;
  border: 2px dashed #cacaca;
}
.list .item .item_img .img.data-v-53459e40 {
  width: 50%;
  height: 50%;
}
.showAll.data-v-53459e40 {
  width: 100%;
  height: 70rpx;
  margin-bottom: 20rpx;
}
.showAll .showAll-icon.data-v-53459e40 {
  width: 34rpx;
  height: 34rpx;
}
.list-option.data-v-53459e40 {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 0 30rpx;
}
.list-option .item.data-v-53459e40 {
  box-sizing: border-box;
  padding-right: 20rpx;
  width: 100%;
  height: 100rpx;
}
.list-option .item .item-subtitle.data-v-53459e40 {
  height: 40rpx;
  line-height: 40rpx;
}
.list-option .item .item-subtitle .img.data-v-53459e40 {
  width: 40rpx;
  height: 40rpx;
}
.list-option .item .item-enter.data-v-53459e40 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 4rpx;
  margin-left: 10rpx;
}
.list-option .list-option-text.data-v-53459e40 {
  position: relative;
  top: -20rpx;
  width: 100%;
  box-sizing: border-box;
  padding-right: 20rpx;
}
