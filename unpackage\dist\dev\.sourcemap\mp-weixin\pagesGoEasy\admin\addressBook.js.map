{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?1fee", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?2a56", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?6f34", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?f384", "uni-app:///pagesGoEasy/admin/addressBook.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?cd7a", "webpack:///D:/Workspace/public/2025/IM-消息系统/huyun-im/pagesGoEasy/admin/addressBook.vue?f5b4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "groups", "onLoad", "methods", "to", "init", "he<PERSON><PERSON>", "enterChat", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAitB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsBruB;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;IACAC;MAAA;MACAC;QACA;MACA;IACA;IACAC;MACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA8gC,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAliC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/admin/addressBook.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/admin/addressBook.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addressBook.vue?vue&type=template&id=4a766084&\"\nvar renderjs\nimport script from \"./addressBook.vue?vue&type=script&lang=js&\"\nexport * from \"./addressBook.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addressBook.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/admin/addressBook.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addressBook.vue?vue&type=template&id=4a766084&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.groups.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addressBook.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addressBook.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<m-return></m-return>\n\t\t<view class=\"contacts\">\n\t\t\t<view class=\"contacts-container\">\n\t\t\t\t<view class=\"user-list\" v-if=\"groups.length\">\n\t\t\t\t\t<view class=\"user-list-item\" v-for=\"(group, key) in groups\" :key=\"key\" @click=\"enterChat(group.group_id)\">\n\t\t\t\t\t\t<view class=\"user-item-avatar\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"group.group_info.avatar\" mode=\"aspectFill\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"user-item-info\">\n\t\t\t\t\t\t\t<span class=\"user-item-info__name\">{{ group.group_info.name }}</span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"nouser-list\" v-else>暂无数据</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { to, jsonUrl } from '@/utils/index.js';\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tgroups: []\n\t\t};\n\t},\n\tonLoad(e) {\n\t\tthis.init();\n\t},\n\tmethods: {\n\t\tto,\n\t\tinit() {\n\t\t\thenglang.get('Group/group', {}, false, (res) => {\n\t\t\t\tthis.groups = res.data.data;\n\t\t\t});\n\t\t},\n\t\tenterChat(group_id) {\r\n\t\t\tconsole.log(group_id)\n\t\t\tto(`/pagesGoEasy/chat_page/index?groupId=${group_id}`);\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.contacts {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding-bottom: 0;\n\tpadding-bottom: constant(safe-area-inset-bottom);\n\tpadding-bottom: env(safe-area-inset-bottom);\n}\n\n.contacts .contacts-container {\n\twidth: 100%;\n\toverflow: auto;\n}\n\n.contacts .user-list-item {\n\tbox-sizing: border-box;\n\theight: 132rpx;\n\tpadding: 32rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.contacts .contacts-title {\n\theight: 80rpx;\n\tline-height: 80rpx;\n\tfont-size: 30rpx;\n\tcolor: #666666;\n\tbackground: #f3f4f7;\n\ttext-indent: 44rpx;\n}\n\n.contacts .user-list {\n\tflex-grow: 1;\n\tbackground: #ffffff;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.nouser-list {\n\twidth: 100%;\n\theight: 200rpx;\n\ttext-align: center;\n\tline-height: 200rpx;\n}\n\n.contacts .user-item-avatar {\n\twidth: 96rpx;\n\theight: 96rpx;\n\tborder-radius: 10rpx;\n\tmargin-right: 32rpx;\n\toverflow: hidden;\n\tposition: relative;\n}\n.contacts .user-item-info {\n\theight: 130rpx;\n\tpadding-right: 32rpx;\n\tline-height: 88rpx;\n\tflex-grow: 1;\n\tborder-bottom: 1px solid #efefef;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.contacts .user-item-info__name {\n\tfont-size: 30rpx;\n\tfont-family: Source Han Sans CN;\n\tfont-style: normal;\n\tfont-weight: bold;\n\tcolor: #262628;\n}\n\n.contacts .user-item-info__tips {\n\theight: 44rpx;\n\twidth: 64rpx;\n\tborder-radius: 24rpx;\n\tfont-size: 26rpx;\n\tline-height: 44rpx;\n\tbackground: #d02129;\n\tcolor: #ffffff;\n\tfont-family: Cabin;\n\ttext-align: center;\n}\n\n.contacts .online-dot {\n\tposition: absolute;\n\twidth: 32rpx !important;\n\theight: 32rpx !important;\n\tright: 0;\n\tbottom: 0;\n}\n\n.contacts .online-tips {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addressBook.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addressBook.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137553152\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}