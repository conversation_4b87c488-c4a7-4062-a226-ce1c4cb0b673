<template>
  <view class="demo-container">
    <view class="header">
      <text class="title">群聊滚动位置记录演示</text>
    </view>
    
    <view class="group-list">
      <view 
        v-for="group in groups" 
        :key="group.id"
        class="group-item"
        @click="enterGroup(group)"
      >
        <view class="group-info">
          <text class="group-name">{{ group.name }}</text>
          <text class="group-id">ID: {{ group.id }}</text>
        </view>
        <view class="scroll-info">
          <text class="scroll-text">滚动位置: {{ getScrollPosition(group.id) }}</text>
        </view>
      </view>
    </view>
    
    <view class="actions">
      <button @click="clearAllPositions" class="clear-btn">清除所有滚动位置</button>
      <button @click="showStorageInfo" class="info-btn">查看存储信息</button>
    </view>
    
    <view v-if="storageInfo" class="storage-info">
      <text class="storage-title">存储信息:</text>
      <text class="storage-content">{{ storageInfo }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ScrollPositionDemo',
  data() {
    return {
      groups: [
        { id: '1001', name: '工作群' },
        { id: '1002', name: '朋友群' },
        { id: '1003', name: '家庭群' },
        { id: '1004', name: '学习群' }
      ],
      storageInfo: ''
    }
  },
  
  methods: {
    /**
     * 进入群聊
     */
    enterGroup(group) {
      const groupInfo = encodeURIComponent(JSON.stringify({
        id: group.id,
        title: group.name,
        userArr: [
          { userId: '123', nickname: '测试用户', avatar: '' }
        ]
      }))
      
      const userInfo = encodeURIComponent(JSON.stringify({
        userId: '123',
        nickname: '测试用户'
      }))
      
      uni.navigateTo({
        url: `/pagesHuYunIm/pages/chat/index?groupInfo=${groupInfo}&userInfo=${userInfo}`
      })
    },
    
    /**
     * 获取群聊的滚动位置
     */
    getScrollPosition(groupId) {
      try {
        const scrollPositions = uni.getStorageSync('group_scroll_positions') || {}
        return scrollPositions[groupId] || 0
      } catch (error) {
        console.error('获取滚动位置失败:', error)
        return 0
      }
    },
    
    /**
     * 清除所有滚动位置
     */
    clearAllPositions() {
      try {
        uni.removeStorageSync('group_scroll_positions')
        uni.showToast({
          title: '已清除所有滚动位置',
          icon: 'success'
        })
        this.$forceUpdate() // 强制更新视图
      } catch (error) {
        console.error('清除滚动位置失败:', error)
        uni.showToast({
          title: '清除失败',
          icon: 'error'
        })
      }
    },
    
    /**
     * 显示存储信息
     */
    showStorageInfo() {
      try {
        const scrollPositions = uni.getStorageSync('group_scroll_positions') || {}
        this.storageInfo = JSON.stringify(scrollPositions, null, 2)
      } catch (error) {
        console.error('获取存储信息失败:', error)
        this.storageInfo = '获取失败'
      }
    }
  },
  
  onShow() {
    // 页面显示时刷新滚动位置信息
    this.$forceUpdate()
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.group-list {
  margin-bottom: 40rpx;
}

.group-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  &:active {
    background-color: #f0f0f0;
  }
}

.group-info {
  .group-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .group-id {
    font-size: 24rpx;
    color: #999;
  }
}

.scroll-info {
  .scroll-text {
    font-size: 28rpx;
    color: #666;
  }
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.clear-btn, .info-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.clear-btn {
  background-color: #ff4757;
  color: white;
}

.info-btn {
  background-color: #5352ed;
  color: white;
}

.storage-info {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .storage-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 20rpx;
  }
  
  .storage-content {
    font-size: 24rpx;
    color: #666;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
